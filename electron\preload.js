const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
    // 工作区管理
    selectWorkspaceFolder: () => ipcRenderer.invoke('select-workspace-folder'),
    checkFolderExists: (folderPath) => ipcRenderer.invoke('check-folder-exists', folderPath),
    createFolder: (folderPath) => ipcRenderer.invoke('create-folder', folderPath),
    readFolder: (folderPath) => ipcRenderer.invoke('read-folder', folderPath),
    
    // 文件操作
    readJsonFile: (filePath) => ipcRenderer.invoke('read-json-file', filePath),
    writeJsonFile: (filePath, data) => ipcRenderer.invoke('write-json-file', filePath, data),
    deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),
    readDirectory: (folderPath) => ipcRenderer.invoke('read-directory', folderPath),
    getImageFiles: (folderPath) => ipcRenderer.invoke('get-image-files', folderPath),
    getJsonFiles: (folderPath) => ipcRenderer.invoke('get-json-files', folderPath),
    
    // 系统操作
    showItemInFolder: (filePath) => ipcRenderer.invoke('show-item-in-folder', filePath),
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    
    // 菜单事件监听
    onMenuOpenWorkspace: (callback) => {
        ipcRenderer.on('menu-open-workspace', callback);
    },
    onMenuSaveCurrent: (callback) => {
        ipcRenderer.on('menu-save-current', callback);
    },
    
    // 移除事件监听器
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    }
});

// 检测是否在Electron环境中
contextBridge.exposeInMainWorld('isElectron', true);

// 提供平台信息
contextBridge.exposeInMainWorld('platform', {
    isWindows: process.platform === 'win32',
    isMac: process.platform === 'darwin',
    isLinux: process.platform === 'linux'
});
