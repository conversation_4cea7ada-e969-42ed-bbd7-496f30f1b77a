# OCR标注工具更新日志

## v2.0.0 (2024-12-19) - 多题目支持重大更新

### 🎉 新功能

#### 多题目标注支持
- ✅ **支持一张图片框选多道题**：移除了每张图片只能标注一道大题的限制
- ✅ **智能题目计数**：顶部工具栏显示当前图片的题目数量
- ✅ **完整数据保存**：保存所有框选的题目数据，不会丢失任何标注
- ✅ **批量导出功能**：导出时包含所有标注的题目

#### 本地文件生成
- ✅ **File System Access API支持**：在支持的浏览器中直接保存到本地文件系统
- ✅ **智能降级方案**：不支持新API的浏览器自动使用下载方式
- ✅ **保存成功提示**：文件保存完成后显示友好提示，无需下载

#### 用户体验改进
- ✅ **快捷键说明按钮**：顶部工具栏新增快捷键说明按钮
- ✅ **F1快捷键支持**：按F1键快速查看快捷键说明
- ✅ **题目计数器**：实时显示当前图片的题目数量
- ✅ **智能文件命名**：导出文件名包含题目数量信息

### 🔧 技术改进

#### 数据结构优化
- 重构了JSON生成逻辑，支持多道题的数据结构
- 改进了标注管理器，移除大题数量限制
- 优化了父子关系判断算法

#### 代码质量提升
- 添加了完整的错误处理和用户反馈
- 改进了坐标转换和边界检查
- 增强了数据验证和完整性检查

### 📁 新增文件

- `test-multiple-questions.html` - 多题目功能测试页面
- `sample-multiple-questions.json` - 多题目数据结构示例
- `CHANGELOG.md` - 更新日志文档

### 🔄 修改的文件

#### 核心功能文件
- `js/main.js` - 添加多题目导出和本地文件保存功能
- `js/data-manager.js` - 重构JSON生成逻辑支持多题目
- `js/annotation-manager.js` - 移除大题数量限制
- `js/ui-manager.js` - 添加题目计数器和快捷键按钮支持

#### 界面文件
- `index.html` - 添加题目计数器和快捷键按钮
- `css/styles.css` - 添加题目计数器样式
- `css/components.css` - 添加快捷键说明弹窗样式

#### 工具文件
- `js/utils.js` - 添加快捷键说明弹窗功能

### 📊 数据格式变化

#### 新的JSON结构支持
```json
{
  "大题1": { ... },
  "大题2": { ... },
  "大题3": { ... }
}
```

#### 文件命名规则
- 旧格式：`图片名_大题1.json`
- 新格式：`图片名_3道题.json`

### 🚀 使用说明

#### 多题目标注流程
1. 导入图片后，可以多次点击"大题"工具
2. 在图片上框选多个大题区域
3. 为每道题添加小题、答题区域等
4. 题目计数器会实时显示当前题目数量
5. 点击"导出JSON"保存所有题目数据

#### 本地文件保存
1. 在支持的浏览器中，导出时会弹出文件保存对话框
2. 选择保存位置和文件名
3. 文件直接保存到指定位置，无需下载

#### 快捷键说明
- 点击顶部工具栏的"快捷键"按钮
- 或按F1键查看完整的快捷键列表

### 🔍 测试验证

#### 功能测试
- 打开 `test-multiple-questions.html` 进行功能测试
- 验证多题目数据结构生成
- 测试文件保存API支持情况

#### 兼容性测试
- Chrome 80+ ✅ (支持File System Access API)
- Firefox 75+ ✅ (降级到下载方式)
- Safari 13+ ✅ (降级到下载方式)
- Edge 80+ ✅ (支持File System Access API)

### 🐛 修复的问题

- 修复了一张图片只能标注一道题的限制
- 修复了保存数据不完整的问题
- 修复了导出文件命名不准确的问题
- 改进了点击检测的准确性和错误处理

### 📈 性能优化

- 优化了大量标注时的渲染性能
- 改进了数据结构的内存使用
- 增强了错误恢复机制

### 🔮 后续计划

- 支持标注模板功能
- 添加批量处理工具
- 集成AI辅助标注
- 支持云端数据同步

---

## v1.0.0 (2024-12-19) - 初始版本

### 基础功能
- 图片导入和管理
- 基础标注功能（大题、小题、答题区域、配图）
- JSON数据导出
- 质检模式
- 快捷键支持
