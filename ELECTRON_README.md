# OCR标注工具 - Electron版本

这是OCR标注工具的Electron桌面应用版本，提供了完整的工作区管理、数据缓存和文件保存功能。

## 功能特性

### 🖥️ 桌面应用
- 原生桌面应用体验
- 完整的文件系统访问权限
- 菜单栏快捷操作
- 跨平台支持（Windows、macOS、Linux）

### 📁 工作区管理
- 选择工作区文件夹
- 自动创建和管理子文件夹结构：
  - `1、交付json/` - 存放生成的JSON文件
  - `2、原始图片/` - 存放待标注的图片
- 工作区状态实时显示
- 支持工作区重新连接

### 💾 数据缓存和保存
- 内存中缓存所有标注数据
- 按大题自动生成JSON文件
- 文件命名格式：`{图片名}大题{编号}.json`
- 支持批量保存所有标注数据
- 支持单独保存当前图片标注

### 🔄 数据持久化
- 自动加载工作区中已有的JSON文件
- 切换图片时自动保存和加载标注数据
- 内存数据管理，支持清除缓存

## 安装和运行

### 方法一：使用启动脚本（推荐）

**Windows用户：**
```bash
双击运行 start.bat
```

**macOS/Linux用户：**
```bash
chmod +x start.sh
./start.sh
```

### 方法二：手动安装

1. 确保已安装Node.js（版本14或更高）
2. 安装依赖：
   ```bash
   npm install
   ```
3. 启动应用：
   ```bash
   npm start
   ```

### 方法三：开发模式
```bash
npm run dev
```

## 使用说明

### 1. 打开工作区
- 点击"打开工作区"按钮或使用菜单 `文件 > 打开工作区`
- 选择一个文件夹作为工作区
- 应用会自动创建必要的子文件夹

### 2. 导入图片
- 将图片文件放入 `2、原始图片/` 文件夹
- 应用会自动检测并加载图片

### 3. 标注操作
- 使用工具栏进行标注（大题、小题、答题区域、配图区域）
- 在右侧面板编辑标注属性
- 标注数据会自动缓存在内存中

### 4. 保存数据
- **保存当前标注**：保存当前图片的所有标注到JSON文件
- **保存所有标注**：批量保存内存中所有图片的标注数据
- JSON文件会自动保存到 `1、交付json/` 文件夹

### 5. 数据管理
- **清除内存**：清除内存中的标注数据（不影响已保存的JSON文件）
- 切换图片时会自动保存当前标注并加载目标图片的标注

## 快捷键

- `Ctrl+O` - 打开工作区
- `Ctrl+S` - 保存当前标注
- `Ctrl+Shift+S` - 保存所有标注
- `1-4` - 选择标注工具
- `V` - 切换标注显示
- `←/→` - 切换图片

## 文件结构

```
工作区文件夹/
├── 1、交付json/          # JSON输出文件夹
│   ├── 图片1大题1.json
│   ├── 图片1大题2.json
│   └── ...
├── 2、原始图片/          # 图片文件夹
│   ├── 图片1.jpg
│   ├── 图片2.png
│   └── ...
```

## 构建发布版本

### 构建所有平台
```bash
npm run build
```

### 构建特定平台
```bash
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
```

构建完成的文件会在 `dist/` 文件夹中。

## 技术架构

- **主进程**：`electron/main.js` - 窗口管理、菜单、文件系统操作
- **预加载脚本**：`electron/preload.js` - 安全的API接口
- **渲染进程**：原有的Web应用代码，通过IPC与主进程通信

## 注意事项

1. 确保工作区文件夹有读写权限
2. JSON文件使用UTF-8编码
3. 图片文件支持常见格式（JPG、PNG、GIF、BMP、WebP）
4. 建议定期备份工作区数据

## 故障排除

### 应用无法启动
- 检查Node.js版本是否为14或更高
- 删除 `node_modules` 文件夹后重新运行 `npm install`

### 工作区无法打开
- 确保选择的文件夹有读写权限
- 检查文件夹路径中是否包含特殊字符

### JSON文件保存失败
- 检查 `1、交付json/` 文件夹是否存在且有写入权限
- 确保磁盘空间充足

## 更新日志

### v1.0.0
- 初始Electron版本发布
- 完整的工作区管理功能
- 数据缓存和批量保存
- 跨平台支持
