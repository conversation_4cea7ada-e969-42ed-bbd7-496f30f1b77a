/**
 * OCR标注工具 - 图片管理器
 * 负责图片的导入、导航、缩放等功能
 */

class ImageManager {
    constructor() {
        this.images = [];
        this.currentIndex = 0;
        this.zoomLevel = 1;
        this.minZoom = 0.1;
        this.maxZoom = 5;
        this.zoomStep = 0.1;

        // 平移相关属性
        this.panX = 0;
        this.panY = 0;
        this.isPanning = false;
        this.lastPanPoint = null;
        
        this.imageElement = null;
        this.canvasElement = null;
        this.coordinateSystem = null;
        
        this.preloadedImages = new Map();
        this.maxPreloadCount = 5;
        
        this.callbacks = {
            onImageChange: [],
            onZoomChange: [],
            onImagesLoad: [],
            onPanEnd: [],
            onPanUpdate: []
        };
        
        this.initializeElements();
        this.setupEventListeners();
    }

    /**
     * 初始化DOM元素
     */
    initializeElements() {
        this.imageElement = document.getElementById('currentImage');
        this.canvasElement = document.getElementById('annotationCanvas');
        
        if (this.imageElement && this.canvasElement) {
            this.coordinateSystem = new CoordinateSystem(this.imageElement, this.canvasElement);
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 图片加载完成事件
        if (this.imageElement) {
            this.imageElement.addEventListener('load', () => {
                this.onImageLoaded();
            });
            
            this.imageElement.addEventListener('error', (e) => {
                console.error('图片加载失败:', e);
                Utils.showNotification('图片加载失败', 'error');
            });
        }

        // 窗口大小改变事件
        window.addEventListener('resize', Utils.debounce(() => {
            this.updateDisplay();
        }, 250));

        // 鼠标滚轮缩放
        if (this.canvasElement) {
            this.canvasElement.addEventListener('wheel', (e) => {
                e.preventDefault();
                const delta = e.deltaY > 0 ? -this.zoomStep : this.zoomStep;
                const mousePos = this.coordinateSystem.eventToImage(e);
                this.zoomAtPoint(mousePos.x, mousePos.y, this.zoomLevel + delta);
            });

            // 平移相关事件
            this.canvasElement.addEventListener('mousedown', (e) => {
                // 只有在没有选择工具时才允许平移（右键或中键）
                if (e.button === 2 || e.button === 1 || (e.button === 0 && e.ctrlKey)) {
                    e.preventDefault();
                    this.startPanning(e);
                }
            });

            this.canvasElement.addEventListener('mousemove', (e) => {
                if (this.isPanning) {
                    e.preventDefault();
                    this.updatePanning(e);
                }
            });

            this.canvasElement.addEventListener('mouseup', (e) => {
                if (this.isPanning) {
                    e.preventDefault();
                    this.stopPanning();
                }
            });

            // 防止右键菜单
            this.canvasElement.addEventListener('contextmenu', (e) => {
                e.preventDefault();
            });

            // 鼠标离开时停止平移
            this.canvasElement.addEventListener('mouseleave', () => {
                if (this.isPanning) {
                    this.stopPanning();
                }
            });
        }
    }

    /**
     * 添加回调函数
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 触发回调函数
     * @param {string} event 事件名称
     * @param {any} data 传递的数据
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    /**
     * 加载图片文件
     * @param {FileList} files 文件列表
     */
    async loadImages(files) {
        const imageFiles = Array.from(files).filter(file => Utils.isImageFile(file));
        
        if (imageFiles.length === 0) {
            Utils.showNotification('未找到有效的图片文件', 'warning');
            return;
        }

        // 按文件名排序
        imageFiles.sort((a, b) => a.name.localeCompare(b.name));

        // 创建图片对象
        this.images = [];
        for (const file of imageFiles) {
            try {
                const url = await Utils.file.readAsDataURL(file);
                this.images.push({
                    id: Utils.generateId(),
                    name: file.name,
                    file: file,
                    url: url,
                    size: file.size,
                    loaded: false
                });
            } catch (error) {
                console.error('读取图片文件失败:', file.name, error);
            }
        }

        if (this.images.length > 0) {
            this.currentIndex = 0;
            await this.loadCurrentImage();
            this.preloadNearbyImages();
            this.updateUI();
            this.trigger('onImagesLoad', this.images);
            Utils.showNotification(`成功加载 ${this.images.length} 张图片`, 'success');
        }
    }

    /**
     * 加载文件夹
     * @param {FileList} files 文件列表（包含目录结构）
     */
    async loadFolder(files) {
        await this.loadImages(files);
    }

    /**
     * 从工作区加载图片对象数组
     * @param {Array} imageObjects 图片对象数组
     */
    async loadImageObjects(imageObjects) {
        if (!Array.isArray(imageObjects) || imageObjects.length === 0) {
            Utils.showNotification('没有有效的图片对象', 'warning');
            return;
        }

        // 直接设置图片数组
        this.images = imageObjects;
        this.currentIndex = 0;

        // 加载第一张图片
        await this.loadCurrentImage();
        this.preloadNearbyImages();
        this.updateUI();
        this.trigger('onImagesLoad', this.images);

        Utils.showNotification(`成功加载 ${this.images.length} 张图片`, 'success');
    }

    /**
     * 加载当前图片
     */
    async loadCurrentImage() {
        if (!this.hasImages()) return;

        const currentImage = this.images[this.currentIndex];
        if (!currentImage) return;

        try {
            // 检查是否已预加载
            if (this.preloadedImages.has(currentImage.id)) {
                this.imageElement.src = currentImage.url;
                currentImage.loaded = true;
            } else {
                // 显示加载状态
                this.showLoadingState();
                
                // 创建新的Image对象进行预加载
                const img = new Image();
                img.onload = () => {
                    this.imageElement.src = currentImage.url;
                    currentImage.loaded = true;
                    this.preloadedImages.set(currentImage.id, img);
                    this.hideLoadingState();
                };
                // img.onerror = () => {
                //     console.error('图片加载失败:', currentImage.name);
                //     Utils.showNotification(`图片加载失败: ${currentImage.name}`, 'error');
                //     this.hideLoadingState();
                // };
                img.src = currentImage.url;
            }
        } catch (error) {
            console.error('加载图片时出错:', error);
            Utils.showNotification('图片加载失败', 'error');
        }
    }

    /**
     * 预加载附近的图片
     */
    preloadNearbyImages() {
        if (!this.hasImages()) return;

        const preloadRange = Math.floor(this.maxPreloadCount / 2);
        const start = Math.max(0, this.currentIndex - preloadRange);
        const end = Math.min(this.images.length - 1, this.currentIndex + preloadRange);

        for (let i = start; i <= end; i++) {
            if (i !== this.currentIndex) {
                this.preloadImage(i);
            }
        }

        // 清理过远的预加载图片
        this.cleanupPreloadedImages();
    }

    /**
     * 预加载指定索引的图片
     * @param {number} index 图片索引
     */
    preloadImage(index) {
        if (index < 0 || index >= this.images.length) return;

        const image = this.images[index];
        if (this.preloadedImages.has(image.id)) return;

        const img = new Image();
        img.onload = () => {
            this.preloadedImages.set(image.id, img);
        };
        img.src = image.url;
    }

    /**
     * 清理预加载的图片
     */
    cleanupPreloadedImages() {
        if (this.preloadedImages.size <= this.maxPreloadCount) return;

        const currentImage = this.getCurrentImage();
        if (!currentImage) return;

        // 保留当前图片和附近的图片
        const keepIds = new Set();
        const preloadRange = Math.floor(this.maxPreloadCount / 2);
        const start = Math.max(0, this.currentIndex - preloadRange);
        const end = Math.min(this.images.length - 1, this.currentIndex + preloadRange);

        for (let i = start; i <= end; i++) {
            keepIds.add(this.images[i].id);
        }

        // 删除不需要的预加载图片
        for (const [id, img] of this.preloadedImages) {
            if (!keepIds.has(id)) {
                this.preloadedImages.delete(id);
                // 释放内存
                img.src = '';
            }
        }
    }

    /**
     * 图片加载完成处理
     */
    onImageLoaded() {
        if (this.coordinateSystem) {
            this.coordinateSystem.updateImageInfo();
        }
        this.updateDisplay();
        this.trigger('onImageChange', this.getCurrentImage());
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        // 使用通知系统显示加载状态
        Utils.showNotification('图片加载中...', 'info', 2000);
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        // 加载完成，不需要特殊处理
    }

    /**
     * 切换到上一张图片
     */
    async previousImage() {
        if (!this.hasImages()) return;
        
        this.currentIndex = this.currentIndex > 0 ? this.currentIndex - 1 : this.images.length - 1;
        await this.loadCurrentImage();
        this.preloadNearbyImages();
        this.updateUI();
    }

    /**
     * 切换到下一张图片
     */
    async nextImage() {
        if (!this.hasImages()) return;
        
        this.currentIndex = this.currentIndex < this.images.length - 1 ? this.currentIndex + 1 : 0;
        await this.loadCurrentImage();
        this.preloadNearbyImages();
        this.updateUI();
    }

    /**
     * 跳转到指定图片
     * @param {number} index 图片索引
     */
    async goToImage(index) {
        if (!this.hasImages() || index < 0 || index >= this.images.length) return;
        
        this.currentIndex = index;
        await this.loadCurrentImage();
        this.preloadNearbyImages();
        this.updateUI();
    }

    /**
     * 放大图片
     */
    zoomIn() {
        this.setZoom(this.zoomLevel + this.zoomStep);
    }

    /**
     * 缩小图片
     */
    zoomOut() {
        this.setZoom(this.zoomLevel - this.zoomStep);
    }

    /**
     * 重置缩放和平移
     */
    resetZoom() {
        this.setZoom(1);
        this.resetPan();
    }

    /**
     * 设置缩放级别
     * @param {number} zoom 缩放级别
     */
    setZoom(zoom) {
        const newZoom = Utils.clamp(zoom, this.minZoom, this.maxZoom);
        if (newZoom !== this.zoomLevel) {
            this.zoomLevel = newZoom;
            this.applyZoom();
            this.trigger('onZoomChange', this.zoomLevel);
        }
    }

    /**
     * 在指定点缩放
     * @param {number} imageX 图片X坐标
     * @param {number} imageY 图片Y坐标
     * @param {number} zoom 缩放级别
     */
    zoomAtPoint(imageX, imageY, zoom) {
        const newZoom = Utils.clamp(zoom, this.minZoom, this.maxZoom);
        if (newZoom !== this.zoomLevel) {
            this.zoomLevel = newZoom;
            // TODO: 实现以指定点为中心的缩放
            this.applyZoom();
            this.trigger('onZoomChange', this.zoomLevel);
        }
    }

    /**
     * 开始平移
     * @param {MouseEvent} e 鼠标事件
     */
    startPanning(e) {
        this.isPanning = true;
        this.lastPanPoint = {
            x: e.clientX,
            y: e.clientY
        };

        // 更改鼠标样式和body class
        document.body.classList.add('panning');
        this.canvasElement.style.cursor = 'grabbing';
    }

    /**
     * 更新平移
     * @param {MouseEvent} e 鼠标事件
     */
    updatePanning(e) {
        if (!this.isPanning || !this.lastPanPoint) return;

        const deltaX = e.clientX - this.lastPanPoint.x;
        const deltaY = e.clientY - this.lastPanPoint.y;

        this.panX += deltaX;
        this.panY += deltaY;

        this.lastPanPoint = {
            x: e.clientX,
            y: e.clientY
        };

        this.applyTransform();

        // 触发实时重绘事件，确保标注框在拖拽过程中也能跟随
        this.trigger('onPanUpdate', { panX: this.panX, panY: this.panY });
    }

    /**
     * 停止平移
     */
    stopPanning() {
        this.isPanning = false;
        this.lastPanPoint = null;

        // 恢复鼠标样式和body class
        document.body.classList.remove('panning');

        // 恢复canvas的鼠标样式，需要检查当前是否有选中的工具
        if (window.ocrTool && window.ocrTool.annotationManager && window.ocrTool.annotationManager.currentTool) {
            this.canvasElement.style.cursor = 'crosshair';
        } else {
            this.canvasElement.style.cursor = 'default';
        }

        // 触发重绘事件，确保标注框正确显示
        this.trigger('onPanEnd', { panX: this.panX, panY: this.panY });
    }

    /**
     * 重置平移
     */
    resetPan() {
        this.panX = 0;
        this.panY = 0;
        this.applyTransform();
    }

    /**
     * 应用缩放和平移变换
     */
    applyTransform() {
        if (this.imageElement) {
            this.imageElement.style.transform = `translate(${this.panX}px, ${this.panY}px) scale(${this.zoomLevel})`;

            // 更新坐标系统的缩放和平移信息
            if (this.coordinateSystem) {
                this.coordinateSystem.setZoom(this.zoomLevel);
                this.coordinateSystem.setPan(this.panX, this.panY);
                // 重要：在变换后更新图片信息
                setTimeout(() => {
                    this.coordinateSystem.updateImageInfo();
                }, 0);
            }

            this.updateDisplay();
        }
    }

    /**
     * 应用缩放（保持向后兼容）
     */
    applyZoom() {
        this.applyTransform();
    }

    /**
     * 更新显示
     */
    updateDisplay() {
        if (this.coordinateSystem) {
            this.coordinateSystem.updateImageInfo();
        }
        this.updateUI();
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 更新图片计数
        const currentImageIndex = document.getElementById('currentImageIndex');
        const totalImages = document.getElementById('totalImages');
        if (currentImageIndex && totalImages) {
            currentImageIndex.textContent = this.hasImages() ? this.currentIndex + 1 : 0;
            totalImages.textContent = this.images.length;
        }

        // 更新图片名称
        const currentImageName = document.getElementById('currentImageName');
        if (currentImageName) {
            const currentImage = this.getCurrentImage();
            currentImageName.textContent = currentImage ? currentImage.name : '未选择图片';
        }

        // 更新缩放级别
        const zoomLevelElement = document.getElementById('zoomLevel');
        if (zoomLevelElement) {
            zoomLevelElement.textContent = Math.round(this.zoomLevel * 100) + '%';
        }

        // 更新导航按钮状态
        const prevButton = document.getElementById('prevImage');
        const nextButton = document.getElementById('nextImage');
        if (prevButton && nextButton) {
            const hasImages = this.hasImages();
            prevButton.disabled = !hasImages;
            nextButton.disabled = !hasImages;
        }

        // 显示/隐藏拖拽区域
        const dropZone = document.getElementById('dropZone');
        if (dropZone) {
            dropZone.classList.toggle('hidden', this.hasImages());
        }
    }

    /**
     * 获取当前图片
     * @returns {Object|null} 当前图片对象
     */
    getCurrentImage() {
        return this.hasImages() ? this.images[this.currentIndex] : null;
    }

    /**
     * 检查是否有图片
     * @returns {boolean} 是否有图片
     */
    hasImages() {
        return this.images.length > 0;
    }

    /**
     * 获取图片列表
     * @returns {Array} 图片列表
     */
    getImages() {
        return [...this.images];
    }

    /**
     * 获取当前索引
     * @returns {number} 当前索引
     */
    getCurrentIndex() {
        return this.currentIndex;
    }

    /**
     * 获取缩放级别
     * @returns {number} 缩放级别
     */
    getZoom() {
        return this.zoomLevel;
    }

    /**
     * 获取坐标系统
     * @returns {CoordinateSystem} 坐标系统实例
     */
    getCoordinateSystem() {
        return this.coordinateSystem;
    }

    /**
     * 清空所有图片
     */
    clear() {
        this.images = [];
        this.currentIndex = 0;
        this.preloadedImages.clear();
        
        if (this.imageElement) {
            this.imageElement.src = '';
        }
        
        this.updateUI();
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.clear();
        this.callbacks = {
            onImageChange: [],
            onZoomChange: [],
            onImagesLoad: []
        };
    }
}

// 导出图片管理器类
window.ImageManager = ImageManager;
