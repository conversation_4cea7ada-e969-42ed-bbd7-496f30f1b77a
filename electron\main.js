const { app, BrowserWindow, Menu, dialog, ipcMain, shell } = require('electron');
const path = require('path');
const fs = require('fs-extra');

class OCRAnnotationApp {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.argv.includes('--dev');
        
        this.init();
    }

    init() {
        // 设置应用事件监听器
        app.whenReady().then(() => {
            this.createWindow();
            this.setupMenu();
            this.setupIPC();
        });

        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
    }

    createWindow() {
        // 创建主窗口
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            // icon: path.join(__dirname, '../assets/icon.png'),
            title: 'OCR标注工具',
            show: false
        });

        // 加载应用页面
        this.mainWindow.loadFile('index.html');

        // 窗口准备好后显示
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            if (this.isDev) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // 处理窗口关闭
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }

    setupMenu() {
        const template = [
            {
                label: '文件',
                submenu: [
                    {
                        label: '打开工作区',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => {
                            this.mainWindow.webContents.send('menu-open-workspace');
                        }
                    },

                    { type: 'separator' },
                    {
                        label: '退出',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: '编辑',
                submenu: [
                    { role: 'undo', label: '撤销' },
                    { role: 'redo', label: '重做' },
                    { type: 'separator' },
                    { role: 'cut', label: '剪切' },
                    { role: 'copy', label: '复制' },
                    { role: 'paste', label: '粘贴' },
                    { role: 'selectall', label: '全选' }
                ]
            },
            {
                label: '视图',
                submenu: [
                    { role: 'reload', label: '重新加载' },
                    { role: 'forceReload', label: '强制重新加载' },
                    { role: 'toggleDevTools', label: '开发者工具' },
                    { type: 'separator' },
                    { role: 'resetZoom', label: '重置缩放' },
                    { role: 'zoomIn', label: '放大' },
                    { role: 'zoomOut', label: '缩小' },
                    { type: 'separator' },
                    { role: 'togglefullscreen', label: '全屏' }
                ]
            },
            {
                label: '帮助',
                submenu: [
                    {
                        label: '关于',
                        click: () => {
                            dialog.showMessageBox(this.mainWindow, {
                                type: 'info',
                                title: '关于OCR标注工具',
                                message: 'OCR标注工具',
                                detail: '版本: 1.0.0\n专业的图片标注系统，用于OCR数据标注和处理。'
                            });
                        }
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupIPC() {
        // 选择工作区文件夹
        ipcMain.handle('select-workspace-folder', async () => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openDirectory'],
                title: '选择工作区文件夹'
            });

            if (!result.canceled && result.filePaths.length > 0) {
                return result.filePaths[0];
            }
            return null;
        });

        // 检查文件夹是否存在
        ipcMain.handle('check-folder-exists', async (event, folderPath) => {
            try {
                const stats = await fs.stat(folderPath);
                return stats.isDirectory();
            } catch (error) {
                return false;
            }
        });

        // 创建文件夹
        ipcMain.handle('create-folder', async (event, folderPath) => {
            try {
                await fs.ensureDir(folderPath);
                return true;
            } catch (error) {
                console.error('创建文件夹失败:', error);
                return false;
            }
        });

        // 读取文件夹内容
        ipcMain.handle('read-folder', async (event, folderPath) => {
            try {
                const items = await fs.readdir(folderPath, { withFileTypes: true });
                return items.map(item => ({
                    name: item.name,
                    isDirectory: item.isDirectory(),
                    path: path.join(folderPath, item.name)
                }));
            } catch (error) {
                console.error('读取文件夹失败:', error);
                return [];
            }
        });

        // 读取JSON文件
        ipcMain.handle('read-json-file', async (event, filePath) => {
            try {
                const content = await fs.readFile(filePath, 'utf8');
                return JSON.parse(content);
            } catch (error) {
                console.error('读取JSON文件失败:', error);
                return null;
            }
        });

        // 写入JSON文件
        ipcMain.handle('write-json-file', async (event, filePath, data) => {
            try {
                await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
                return true;
            } catch (error) {
                console.error('写入JSON文件失败:', error);
                return false;
            }
        });

        // 删除文件
        ipcMain.handle('delete-file', async (event, filePath) => {
            try {
                await fs.remove(filePath);
                return true;
            } catch (error) {
                console.error('删除文件失败:', error);
                return false;
            }
        });

        // 读取目录文件列表
        ipcMain.handle('read-directory', async (event, folderPath) => {
            try {
                const items = await fs.readdir(folderPath);
                return items;
            } catch (error) {
                console.error('读取目录失败:', error);
                return [];
            }
        });

        // 获取图片文件列表
        ipcMain.handle('get-image-files', async (event, folderPath) => {
            try {
                const items = await fs.readdir(folderPath);
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
                
                const imageFiles = items.filter(item => {
                    const ext = path.extname(item).toLowerCase();
                    return imageExtensions.includes(ext);
                }).map(item => ({
                    name: item,
                    path: path.join(folderPath, item)
                }));

                return imageFiles;
            } catch (error) {
                console.error('获取图片文件失败:', error);
                return [];
            }
        });

        // 获取JSON文件列表
        ipcMain.handle('get-json-files', async (event, folderPath) => {
            try {
                const items = await fs.readdir(folderPath);
                const jsonFiles = items.filter(item => 
                    path.extname(item).toLowerCase() === '.json'
                ).map(item => ({
                    name: item,
                    path: path.join(folderPath, item)
                }));

                return jsonFiles;
            } catch (error) {
                console.error('获取JSON文件失败:', error);
                return [];
            }
        });

        // 在文件管理器中显示文件
        ipcMain.handle('show-item-in-folder', async (event, filePath) => {
            shell.showItemInFolder(filePath);
        });

        // 打开外部链接
        ipcMain.handle('open-external', async (event, url) => {
            shell.openExternal(url);
        });
    }
}

// 创建应用实例
new OCRAnnotationApp();
