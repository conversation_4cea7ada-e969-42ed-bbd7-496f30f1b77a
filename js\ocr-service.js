/**
 * OCR标注工具 - OCR服务模块
 * 负责图像裁剪、批量OCR调用和结果解析
 */

class OCRService {
    constructor() {
        this.isProcessing = false;
        this.currentProgress = 0;
        this.totalItems = 0;
        
        // OCR配置
        this.config = {
            doubao: {
                baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
                apiKey: 'ba6f3697-0fe3-4651-b6c9-e9e12325c17d',
                models: {
                    'doubao-seed-1-6-flash-250715': 'doubao-seed-1-6-flash-250715',
                    'doubao-seed-1-6-250615': 'doubao-seed-1-6-250615',
                    'doubao-1-5-thinking-vision-pro-250428': 'doubao-1-5-thinking-vision-pro-250428',
                    'doubao-1.5-vision-pro-250328' : 'doubao-1.5-vision-pro-250328',
                    'doubao-1.5-vision-lite-250315': 'doubao-1.5-vision-lite-250315'
                },
                defaultModel: 'doubao-seed-1-6-250615'
            },
            qwen: {
                baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                apiKey: '',
                models: {
                    // 'qwen-vl-plus-latest': 'qwen-vl-plus-latest',
                    // 'qwen-vl-ocr-latest': 'qwen-vl-ocr-latest'
                },
                defaultModel: 'qwen-vl-plus-latest'
            }
        };

        // 当前使用的模型
        this.currentModel = 'doubao-seed-1-6-250615';

        // 单图片OCR提示词
        this.singlePrompt = "请你作为一个OCR识别工具，只提取图像中的文字内容，不进行解释或解答。具体要求如下：1. 普通文本保持原有格式，包括换行；2. 遇到数学公式时，请使用 LaTeX 语言准确表达，使用$包裹公式；3. 遇到表格时，请使用 Markdown 表格语法准确还原；4. 不要输出任何题目解析、推理或答案；5. 只输出识别内容，不添加任何多余说明。";

        // 批量OCR提示词
        this.batchPrompt = `我将提供多张图片（可能包含文字、公式或表格），请你逐张识别内容。
要求如下：
1. 遇到公式，请用 LaTeX 表示。
2. 遇到表格，请用 Markdown 表格格式输出。
3. 普通文本按自然段输出。
每张图片按如下格式输出：
【图片N】
<识别结果>`;
        
        this.callbacks = {
            onProgress: [],
            onComplete: [],
            onError: []
        };

        // 批量处理配置
        this.batchConfig = {
            useTrueBatch: true, // 是否使用真正的批量OCR（一次API调用多图片）
            batchDelay: 1000    // 批次间延迟（毫秒）
        };

        // 图像处理配置
        this.imageConfig = {
            minSize: 100,          // 最小边长阈值，小于此值时自动放大
            jpegQuality: 0.95,     // JPEG质量（当使用JPEG格式时）
            enableSharpening: false, // 是否启用图像锐化
            contrastBoost: 0,      // 对比度增强 (-100 到 100)
            brightnessBoost: 0     // 亮度增强 (-100 到 100)
        };
    }

    /**
     * 添加回调函数
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 设置批量处理配置
     * @param {Object} config 配置对象
     * @param {boolean} config.useTrueBatch 是否使用真正的批量OCR
     */
    setBatchConfig(config) {
        this.batchConfig = { ...this.batchConfig, ...config };
        console.log('批量处理配置已更新:', this.batchConfig);
    }

    /**
     * 获取批量处理配置
     * @returns {Object} 当前配置
     */
    getBatchConfig() {
        return { ...this.batchConfig };
    }

    /**
     * 设置当前使用的模型
     * @param {string} model 模型ID
     */
    setModel(model) {
        this.currentModel = model;
        console.log('OCR模型已更新:', model);
    }

    /**
     * 获取当前使用的模型
     * @returns {string} 当前模型ID
     */
    getModel() {
        return this.currentModel;
    }

    /**
     * 根据模型ID获取提供商
     * @param {string} modelId 模型ID
     * @returns {string} 提供商名称
     */
    getProviderFromModel(modelId) {
        if (modelId.includes('doubao')) {
            return 'doubao';
        } else if (modelId.includes('qwen')) {
            return 'qwen';
        } else {
            // 默认返回doubao
            return 'doubao';
        }
    }

    /**
     * 获取所有可用的模型列表
     * @returns {Array} 模型列表，格式：[{id: 'model-id', name: 'Model Name', provider: 'provider'}]
     */
    getAvailableModels() {
        const models = [];

        // 添加豆包模型
        Object.keys(this.config.doubao.models).forEach(modelId => {
            models.push({
                id: modelId,
                name: modelId,
                provider: 'doubao'
            });
        });

        // 添加通义千问模型
        Object.keys(this.config.qwen.models).forEach(modelId => {
            models.push({
                id: modelId,
                name: modelId,
                provider: 'qwen'
            });
        });

        return models;
    }

    /**
     * 设置图像处理配置
     * @param {Object} config 配置对象
     * @param {number} config.minSize 最小边长阈值
     * @param {number} config.jpegQuality JPEG质量
     * @param {boolean} config.enableSharpening 是否启用锐化
     * @param {number} config.contrastBoost 对比度增强
     * @param {number} config.brightnessBoost 亮度增强
     */
    setImageConfig(config) {
        this.imageConfig = { ...this.imageConfig, ...config };
        console.log('图像处理配置已更新:', this.imageConfig);
    }

    /**
     * 获取图像处理配置
     * @returns {Object} 当前配置
     */
    getImageConfig() {
        return { ...this.imageConfig };
    }

    /**
     * 触发回调函数
     * @param {string} event 事件名称
     * @param {any} data 传递的数据
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    /**
     * 从画布裁剪指定区域的图像
     * @param {HTMLCanvasElement} canvas 画布元素
     * @param {Array} coordinates 坐标 [[x1, y1], [x2, y2]]
     * @returns {string} Base64编码的图像数据
     */
    cropImageFromCanvas(canvas, coordinates) {
        const [[x1, y1], [x2, y2]] = coordinates;
        
        // 确保坐标顺序正确
        const left = Math.min(x1, x2);
        const top = Math.min(y1, y2);
        const width = Math.abs(x2 - x1);
        const height = Math.abs(y2 - y1);
        
        // 创建临时画布
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        
        tempCanvas.width = width;
        tempCanvas.height = height;
        
        // 裁剪图像
        tempCtx.drawImage(canvas, left, top, width, height, 0, 0, width, height);
        
        // 转换为Base64
        return tempCanvas.toDataURL('image/png').split(',')[1];
    }

    /**
     * 从图像元素裁剪指定区域的图像
     * @param {HTMLImageElement} imageElement 图像元素
     * @param {Array} coordinates 坐标 [[x1, y1], [x2, y2]] (图像原始坐标系)
     * @returns {string} Base64编码的图像数据
     */
    cropImageFromElement(imageElement, coordinates) {
        const [[x1, y1], [x2, y2]] = coordinates;

        // 标注坐标已经是图像原始坐标系，无需转换
        // 确保坐标顺序正确
        const left = Math.max(0, Math.min(x1, x2));
        const top = Math.max(0, Math.min(y1, y2));
        const width = Math.min(imageElement.naturalWidth - left, Math.abs(x2 - x1));
        const height = Math.min(imageElement.naturalHeight - top, Math.abs(y2 - y1));



        // 验证裁剪区域
        if (width <= 0 || height <= 0) {
            console.warn('裁剪区域无效:', { left, top, width, height, coordinates });
            // 返回一个1x1的透明图像作为fallback
            const fallbackCanvas = document.createElement('canvas');
            fallbackCanvas.width = 1;
            fallbackCanvas.height = 1;
            return fallbackCanvas.toDataURL('image/png').split(',')[1];
        }

        // 创建临时画布
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');

        // 智能放大：当最短边小于阈值时自动放大
        const minSide = Math.min(width, height);
        const minSizeThreshold = this.imageConfig.minSize;
        let scaleFactor = 1.0;

        if (minSide < minSizeThreshold) {
            scaleFactor = minSizeThreshold / minSide;
            console.log(`检测到小尺寸图片 (最短边: ${minSide}px)，自动放大 ${scaleFactor.toFixed(2)}x`);
        }

        const scaledWidth = Math.round(width * scaleFactor);
        const scaledHeight = Math.round(height * scaleFactor);

        tempCanvas.width = scaledWidth;
        tempCanvas.height = scaledHeight;

        // 设置高质量的图像缩放
        tempCtx.imageSmoothingEnabled = true;
        tempCtx.imageSmoothingQuality = 'high';

        // 裁剪并放大图像
        tempCtx.drawImage(
            imageElement,
            left, top, width, height,           // 源区域
            0, 0, scaledWidth, scaledHeight     // 目标区域（放大后）
        );

        // 应用图像增强
        this.applyImageEnhancements(tempCtx, scaledWidth, scaledHeight);

        // 记录处理日志
        if (scaleFactor > 1) {
            console.log(`图片智能放大: ${width}x${height} -> ${scaledWidth}x${scaledHeight} (${scaleFactor.toFixed(2)}x)`);
        }

        // 转换为Base64
        return tempCanvas.toDataURL('image/png').split(',')[1];
    }

    /**
     * 应用图像增强
     * @param {CanvasRenderingContext2D} ctx 画布上下文
     * @param {number} width 图像宽度
     * @param {number} height 图像高度
     */
    applyImageEnhancements(ctx, width, height) {
        const config = this.imageConfig;

        // 如果没有启用任何增强，直接返回
        if (!config.enableSharpening && config.contrastBoost === 0 && config.brightnessBoost === 0) {
            return;
        }

        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;

        // 应用亮度和对比度调整
        if (config.brightnessBoost !== 0 || config.contrastBoost !== 0) {
            const brightness = config.brightnessBoost / 100 * 255;
            const contrast = (config.contrastBoost + 100) / 100;

            for (let i = 0; i < data.length; i += 4) {
                // 应用对比度和亮度调整 (RGB通道)
                for (let j = 0; j < 3; j++) {
                    let value = data[i + j];
                    // 对比度调整
                    value = (value - 128) * contrast + 128;
                    // 亮度调整
                    value += brightness;
                    // 限制在0-255范围内
                    data[i + j] = Math.max(0, Math.min(255, value));
                }
            }
        }

        // 应用锐化滤镜
        if (config.enableSharpening) {
            this.applySharpeningFilter(data, width, height);
        }

        // 将处理后的数据写回画布
        ctx.putImageData(imageData, 0, 0);
    }

    /**
     * 应用锐化滤镜
     * @param {Uint8ClampedArray} data 图像数据
     * @param {number} width 图像宽度
     * @param {number} height 图像高度
     */
    applySharpeningFilter(data, width, height) {
        // 锐化卷积核
        const kernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];

        const output = new Uint8ClampedArray(data);

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) { // RGB通道
                    let sum = 0;
                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const idx = ((y + ky) * width + (x + kx)) * 4 + c;
                            const kernelIdx = (ky + 1) * 3 + (kx + 1);
                            sum += data[idx] * kernel[kernelIdx];
                        }
                    }
                    const outputIdx = (y * width + x) * 4 + c;
                    output[outputIdx] = Math.max(0, Math.min(255, sum));
                }
            }
        }

        // 复制处理后的数据
        for (let i = 0; i < data.length; i++) {
            data[i] = output[i];
        }
    }

    /**
     * 调用豆包OCR API
     * @param {string} base64Image Base64编码的图像
     * @param {string} modelId 模型ID
     * @returns {Promise<Object>} OCR结果
     */
    async callDoubaoOCR(base64Image, modelId = null) {
        const config = this.config.doubao;
        const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel;

        try {
            const response = await fetch(`${config.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: selectedModel,
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'image_url',
                                    image_url: {
                                        url: `data:image/png;base64,${base64Image}`
                                    }
                                },
                                {
                                    type: 'text',
                                    text: this.singlePrompt
                                }
                            ]
                        }
                    ],
                    thinking: {
                        type: 'disabled'
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            const totalTokens = data.usage?.total_tokens || 0;

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return {
                    success: true,
                    data: {
                        text: data.choices[0].message.content,
                        tokens: totalTokens,
                        provider: 'doubao'
                    }
                };
            } else {
                throw new Error('API响应格式错误');
            }
        } catch (error) {
            console.error('豆包OCR调用失败:', error);
            return {
                success: false,
                error: `OCR识别失败: ${error.message}`
            };
        }
    }

    /**
     * 批量调用豆包OCR API
     * @param {Array} base64Images Base64编码的图像数组
     * @param {string} modelId 模型ID
     * @returns {Promise<Object>} OCR结果
     */
    async callDoubaoOCRBatch(base64Images, modelId = null) {
        const config = this.config.doubao;
        const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel;

        try {
            // 构建多图片内容数组
            const content = [];

            // 添加所有图片
            base64Images.forEach((base64Image) => {
                content.push({
                    type: 'image_url',
                    image_url: {
                        url: `data:image/png;base64,${base64Image}`
                    }
                });
            });

            // 添加提示词
            content.push({
                type: 'text',
                text: this.batchPrompt
            });

            const response = await fetch(`${config.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: selectedModel,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    thinking: {
                        type: 'disabled'
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            const totalTokens = data.usage?.total_tokens || 0;

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return {
                    success: true,
                    data: {
                        text: data.choices[0].message.content,
                        tokens: totalTokens,
                        provider: 'doubao',
                        imageCount: base64Images.length
                    }
                };
            } else {
                throw new Error('API响应格式错误');
            }
        } catch (error) {
            console.error('豆包批量OCR调用失败:', error);
            return {
                success: false,
                error: `批量OCR识别失败: ${error.message}`
            };
        }
    }

    /**
     * 调用阿里云OCR API
     * @param {string} base64Image Base64编码的图像
     * @param {string} modelId 模型ID
     * @returns {Promise<Object>} OCR结果
     */
    async callQwenOCR(base64Image, modelId = null) {
        const config = this.config.qwen;
        const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel;

        try {
            const response = await fetch(`${config.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: selectedModel,
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'image_url',
                                    image_url: {
                                        url: `data:image/png;base64,${base64Image}`
                                    }
                                },
                                {
                                    type: 'text',
                                    text: this.singlePrompt
                                }
                            ]
                        }
                    ]
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            const totalTokens = data.usage?.total_tokens || 0;

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return {
                    success: true,
                    data: {
                        text: data.choices[0].message.content,
                        tokens: totalTokens,
                        provider: 'qwen'
                    }
                };
            } else {
                throw new Error('API响应格式错误');
            }
        } catch (error) {
            console.error('阿里云OCR调用失败:', error);
            return {
                success: false,
                error: `OCR识别失败: ${error.message}`
            };
        }
    }

    /**
     * 批量调用阿里云Qwen OCR API
     * @param {Array} base64Images Base64编码的图像数组
     * @param {string} modelId 模型ID
     * @returns {Promise<Object>} OCR结果
     */
    async callQwenOCRBatch(base64Images, modelId = null) {
        const config = this.config.qwen;
        const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel;

        try {
            // 构建多图片内容数组
            const content = [];

            // 添加所有图片
            base64Images.forEach((base64Image) => {
                content.push({
                    type: 'image_url',
                    image_url: {
                        url: `data:image/png;base64,${base64Image}`
                    }
                });
            });

            // 添加提示词
            content.push({
                type: 'text',
                text: this.batchPrompt
            });

            const response = await fetch(`${config.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: selectedModel,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ]
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();
            const totalTokens = data.usage?.total_tokens || 0;

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return {
                    success: true,
                    data: {
                        text: data.choices[0].message.content,
                        tokens: totalTokens,
                        provider: 'qwen',
                        imageCount: base64Images.length
                    }
                };
            } else {
                throw new Error('API响应格式错误');
            }
        } catch (error) {
            console.error('Qwen批量OCR调用失败:', error);
            return {
                success: false,
                error: `批量OCR识别失败: ${error.message}`
            };
        }
    }

    /**
     * 批量OCR处理
     * @param {Array} imageDataList 图像数据列表 [{base64: string, annotation: Object}]
     * @param {string} provider OCR提供商 ('doubao' | 'qwen')
     * @param {string} modelId 模型ID
     * @returns {Promise<Array>} OCR结果列表
     */
    async batchOCR(imageDataList, provider = 'doubao', modelId = null) {
        if (this.isProcessing) {
            throw new Error('OCR处理正在进行中，请稍后再试');
        }

        this.isProcessing = true;
        this.currentProgress = 0;
        this.totalItems = imageDataList.length;

        const results = [];
        const batchSize = 5; // 每批处理5张图片，避免API限制

        try {
            for (let i = 0; i < imageDataList.length; i += batchSize) {
                const batch = imageDataList.slice(i, i + batchSize);
                const batchPromises = batch.map(async (item, index) => {
                    try {
                        let result;
                        if (provider === 'doubao') {
                            result = await this.callDoubaoOCR(item.base64, modelId);
                        } else if (provider === 'qwen') {
                            result = await this.callQwenOCR(item.base64, modelId);
                        } else {
                            throw new Error(`不支持的OCR提供商: ${provider}`);
                        }

                        return {
                            annotation: item.annotation,
                            result: result,
                            index: i + index
                        };
                    } catch (error) {
                        console.error(`OCR处理失败 (索引 ${i + index}):`, error);
                        return {
                            annotation: item.annotation,
                            result: {
                                success: false,
                                error: error.message
                            },
                            index: i + index
                        };
                    }
                });

                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);

                // 更新进度
                this.currentProgress = Math.min(results.length, this.totalItems);
                this.trigger('onProgress', {
                    current: this.currentProgress,
                    total: this.totalItems,
                    percentage: Math.round((this.currentProgress / this.totalItems) * 100)
                });

                // 批次间延迟，避免API限制
                if (i + batchSize < imageDataList.length) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            this.trigger('onComplete', results);
            return results;

        } catch (error) {
            this.trigger('onError', error);
            throw error;
        } finally {
            this.isProcessing = false;
            this.currentProgress = 0;
            this.totalItems = 0;
        }
    }

    /**
     * 解析批量OCR结果
     * @param {string} batchResult 批量OCR的文本结果
     * @param {Array} imageDataList 图像数据列表（保持顺序）
     * @returns {Array} 解析后的结果数组
     */
    parseBatchOCRResult(batchResult, imageDataList) {
        const results = [];

        try {
            // 按【图片N】分割结果
            const sections = batchResult.split(/【图片\d+】/).filter(section => section.trim());

            console.log('批量OCR解析:', {
                原始结果长度: batchResult.length,
                分割后段落数: sections.length,
                图片数量: imageDataList.length
            });

            // 确保结果数量匹配
            for (let i = 0; i < imageDataList.length; i++) {
                const imageData = imageDataList[i];
                let ocrText = '';

                if (i < sections.length) {
                    ocrText = sections[i].trim();
                } else {
                    console.warn(`图片${i + 1}没有对应的OCR结果`);
                }

                results.push({
                    annotation: imageData.annotation,
                    result: {
                        success: ocrText.length > 0,
                        data: {
                            text: ocrText,
                            tokens: 0, // 批量处理时单个图片的token数无法准确计算
                            provider: imageData.provider || 'unknown'
                        },
                        error: ocrText.length === 0 ? '未找到对应的OCR结果' : null
                    }
                });
            }

        } catch (error) {
            console.error('批量OCR结果解析失败:', error);

            // 如果解析失败，为每个图片返回错误结果
            imageDataList.forEach(imageData => {
                results.push({
                    annotation: imageData.annotation,
                    result: {
                        success: false,
                        error: `结果解析失败: ${error.message}`
                    }
                });
            });
        }

        return results;
    }

    /**
     * 为大题执行一键OCR（包括题干文字框触发的OCR）
     * @param {Object} targetAnnotation 目标标注（大题或题干文字框）
     * @param {Array} allAnnotations 所有标注数组
     * @param {HTMLImageElement} imageElement 图像元素
     * @param {string} modelId 模型ID
     * @returns {Promise<Object>} 处理结果
     */
    async processMainQuestionOCR(targetAnnotation, allAnnotations, imageElement, modelId = null) {
        // 如果是题干文字框，找到对应的大题
        let mainQuestionAnnotation = targetAnnotation;
        if (targetAnnotation.type === 'question-text') {
            mainQuestionAnnotation = allAnnotations.find(ann => ann.id === targetAnnotation.parentId);
            if (!mainQuestionAnnotation) {
                throw new Error('找不到题干文字框对应的大题');
            }
        }
        try {
            // 获取大题的所有子元素
            const subQuestions = allAnnotations.filter(ann =>
                ann.type === 'sub-question' && ann.parentId === mainQuestionAnnotation.id
            ).sort((a, b) => a.number - b.number);

            const answerAreas = [];
            const imageAreas = allAnnotations.filter(ann =>
                ann.type === 'image-area' && ann.parentId === mainQuestionAnnotation.id
            ).sort((a, b) => a.number - b.number);

            // 收集所有答题区域
            subQuestions.forEach(subQ => {
                const subAnswerAreas = allAnnotations.filter(ann =>
                    ann.type === 'answer-area' && ann.parentId === subQ.id
                ).sort((a, b) => a.number - b.number);
                answerAreas.push(...subAnswerAreas);
            });

            // 准备需要OCR的区域列表
            const ocrItems = [];

            // 获取大题的题干文字框
            const questionTexts = allAnnotations.filter(ann =>
                ann.type === 'question-text' && ann.parentId === mainQuestionAnnotation.id
            ).sort((a, b) => a.number - b.number);

            // 添加题干文字框（替代原来的大题题干）
            questionTexts.forEach(questionText => {
                ocrItems.push({
                    type: 'question-text',
                    annotation: questionText,
                    base64: this.cropImageFromElement(imageElement, questionText.coordinates)
                });
            });

            // 添加小题题干
            subQuestions.forEach(subQ => {
                ocrItems.push({
                    type: 'sub-question',
                    annotation: subQ,
                    base64: this.cropImageFromElement(imageElement, subQ.coordinates)
                });
            });

            // 添加答题区域
            answerAreas.forEach(answerArea => {
                ocrItems.push({
                    type: 'answer-area',
                    annotation: answerArea,
                    base64: this.cropImageFromElement(imageElement, answerArea.coordinates)
                });
            });

            // 添加配图区域
            imageAreas.forEach(imageArea => {
                ocrItems.push({
                    type: 'image-area',
                    annotation: imageArea,
                    base64: this.cropImageFromElement(imageElement, imageArea.coordinates)
                });
            });

            console.log(`开始批量OCR处理，共 ${ocrItems.length} 个区域`);

            let displayResults = [];

            if (this.batchConfig.useTrueBatch) {
                // 使用真正的批量OCR（一次API调用处理所有图片）
                console.log('使用真正的批量OCR处理');

                let batchResult;
                const base64Images = ocrItems.map(item => item.base64);
                const currentModel = modelId || this.currentModel;
                const provider = this.getProviderFromModel(currentModel);

                if (provider === 'doubao') {
                    batchResult = await this.callDoubaoOCRBatch(base64Images, currentModel);
                } else if (provider === 'qwen') {
                    batchResult = await this.callQwenOCRBatch(base64Images, currentModel);
                } else {
                    throw new Error(`不支持的OCR提供商: ${provider}`);
                }

                if (batchResult.success) {
                    // 解析批量结果
                    const parsedResults = this.parseBatchOCRResult(batchResult.data.text, ocrItems.map(item => ({
                        annotation: item.annotation,
                        provider: provider
                    })));

                    // 转换为UI显示格式
                    displayResults = parsedResults.map(item => ({
                        annotationId: item.annotation.id,
                        type: item.annotation.type,
                        success: item.result.success,
                        ocrText: item.result.success ? item.result.data.text : null,
                        error: item.result.success ? null : item.result.error,
                        provider: provider,
                        tokens: Math.round((batchResult.data.tokens || 0) / ocrItems.length) // 平均分配token数
                    }));

                    console.log(`真正批量OCR完成，总token: ${batchResult.data.tokens}, 平均每个区域: ${Math.round((batchResult.data.tokens || 0) / ocrItems.length)}`);
                } else {
                    // 批量OCR失败，为每个区域返回错误结果
                    displayResults = ocrItems.map(item => ({
                        annotationId: item.annotation.id,
                        type: item.annotation.type,
                        success: false,
                        ocrText: null,
                        error: batchResult.error,
                        provider: provider,
                        tokens: 0
                    }));
                }
            } else {
                // 使用传统的逐个处理方式（兼容性备用）
                console.log('使用传统的逐个OCR处理');
                const currentModel = modelId || this.currentModel;
                const provider = this.getProviderFromModel(currentModel);

                const ocrResults = await this.batchOCR(ocrItems, provider, currentModel);

                // 转换为UI显示格式
                displayResults = ocrResults.map(item => ({
                    annotationId: item.annotation.id,
                    type: item.annotation.type,
                    success: item.result.success,
                    ocrText: item.result.success ? item.result.data.text : null,
                    error: item.result.success ? null : item.result.error,
                    provider: provider,
                    tokens: item.result.success ? (item.result.data.tokens || 0) : 0
                }));
            }

            return displayResults;

        } catch (error) {
            console.error('一键OCR处理失败:', error);
            // 返回空数组，避免UI处理时出错
            return [];
        }
    }

    /**
     * 解析OCR结果并生成标注更新数据
     * @param {Array} ocrResults OCR结果列表
     * @param {Array} allAnnotations 所有标注列表
     * @returns {Array} 更新结果列表
     */
    parseAndUpdateAnnotations(ocrResults, allAnnotations = []) {
        const updateResults = [];

        ocrResults.forEach(item => {
            if (!item.result.success) {
                updateResults.push({
                    annotationId: item.annotation.id,
                    type: item.annotation.type,
                    success: false,
                    error: item.result.error
                });
                return;
            }

            const ocrText = item.result.data.text.trim();
            const annotation = item.annotation;
            let updateData = {};

            // 根据标注类型处理OCR结果
            switch (annotation.type) {
                case 'question-text':
                    updateData = {
                        content: ocrText
                    };

                    // 同时更新对应的大题
                    const parentMainQuestion = allAnnotations.find(ann => ann.id === annotation.parentId);
                    if (parentMainQuestion) {
                        parentMainQuestion.attributes.content = ocrText;
                        updateResults.push({
                            annotationId: parentMainQuestion.id,
                            type: parentMainQuestion.type,
                            success: true,
                            ocrText: ocrText
                        });
                    }
                    break;

                case 'sub-question':
                    updateData = {
                        content: ocrText
                    };
                    break;

                case 'answer-area':
                    updateData = {
                        answerContent: ocrText,
                        content: ocrText
                    };
                    break;

                case 'image-area':
                    // 配图区域通常不需要OCR文字，但可以记录描述
                    updateData = {
                        description: ocrText
                    };
                    break;

                default:
                    console.warn(`未知的标注类型: ${annotation.type}`);
                    break;
            }

            updateResults.push({
                annotationId: annotation.id,
                type: annotation.type,
                success: true,
                updateData: updateData,
                ocrText: ocrText,
                tokens: item.result.data.tokens,
                provider: item.result.data.provider
            });
        });

        return updateResults;
    }

    /**
     * 获取当前处理状态
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isProcessing: this.isProcessing,
            currentProgress: this.currentProgress,
            totalItems: this.totalItems,
            percentage: this.totalItems > 0 ? Math.round((this.currentProgress / this.totalItems) * 100) : 0
        };
    }

    /**
     * 取消当前处理
     */
    cancel() {
        this.isProcessing = false;
        this.currentProgress = 0;
        this.totalItems = 0;
    }
}
