{"name": "ocr-annotation-tool", "version": "1.0.0", "description": "OCR标注工具 - 专业的图片标注系统", "main": "electron/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder"}, "keywords": ["ocr", "annotation", "electron", "image-processing", "machine-learning"], "author": "OCR Annotation Tool Team", "license": "MIT", "devDependencies": {"electron": "^22.0.0", "electron-builder": "^23.6.0"}, "dependencies": {"fs-extra": "^10.1.0", "node-fetch": "^2.7.0"}, "build": {"appId": "com.ocrtools.annotation", "productName": "OCR标注工具", "directories": {"output": "dist"}, "files": ["electron/**/*", "js/**/*", "css/**/*", "index.html", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}